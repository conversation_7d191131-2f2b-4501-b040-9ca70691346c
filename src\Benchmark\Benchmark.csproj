<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <Optimize>true</Optimize>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="BenchmarkDotNet" Version="0.14.0" />
    </ItemGroup>
        
    <ItemGroup>
      <PackageReference Include="MediatR" Version="12.5.0" />
      <!-- <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" /> -->
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="Mediator.Abstractions" Version="2.1.7" />
        <PackageReference Include="Mediator.SourceGenerator" Version="2.1.7">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DispatchR\DispatchR.csproj" />
    </ItemGroup>

</Project>
