using System.Collections.Concurrent;

namespace DispatchR.Internal;

/// <summary>
/// Provides cached type information and interface analysis for efficient type operations.
/// </summary>
internal sealed class TypeInfo
{
    private static readonly ConcurrentDictionary<Type, Type[]> InterfaceCache = new();
    private static readonly ConcurrentDictionary<(Type type, Type interfaceType), Type?> GenericInterfaceCache = new();
    
    public Type Type { get; }
    public Guid Key { get; }
    public Type[] Interfaces { get; }

    public TypeInfo(Type type)
    {
        Type = type ?? throw new ArgumentNullException(nameof(type));
        Key = type.GUID;
        Interfaces = GetCachedInterfaces(type);
    }

    /// <summary>
    /// Checks if the type implements the specified generic interface definition.
    /// </summary>
    /// <param name="interfaceType">The generic interface definition to check for.</param>
    /// <returns>True if the type implements the interface, false otherwise.</returns>
    public bool ImplementsInterface(Type interfaceType)
    {
        ArgumentNullException.ThrowIfNull(interfaceType, nameof(interfaceType));
        
        if (!interfaceType.IsGenericTypeDefinition)
        {
            throw new ArgumentException("Interface type must be a generic type definition.", nameof(interfaceType));
        }

        return Interfaces
            .Where(i => i.IsGenericType)
            .Any(i => i.GetGenericTypeDefinition() == interfaceType);
    }

    /// <summary>
    /// Gets the first generic interface that matches the specified generic type definition.
    /// </summary>
    /// <param name="interfaceType">The generic interface definition to find.</param>
    /// <returns>The matching interface type, or null if not found.</returns>
    public Type? GetGenericInterface(Type interfaceType)
    {
        ArgumentNullException.ThrowIfNull(interfaceType, nameof(interfaceType));

        return GenericInterfaceCache.GetOrAdd((Type, interfaceType), key =>
        {
            var (type, targetInterface) = key;
            return type.GetInterfaces()
                .FirstOrDefault(i => i.IsGenericType && i.GetGenericTypeDefinition() == targetInterface);
        });
    }

    /// <summary>
    /// Gets the first generic interface that matches any of the specified generic type definitions.
    /// </summary>
    /// <param name="interfaceTypes">The generic interface definitions to search for.</param>
    /// <returns>The first matching interface type, or null if none found.</returns>
    public Type? GetFirstMatchingGenericInterface(params Type[] interfaceTypes)
    {
        ArgumentNullException.ThrowIfNull(interfaceTypes, nameof(interfaceTypes));

        foreach (var interfaceType in interfaceTypes)
        {
            var match = GetGenericInterface(interfaceType);
            if (match != null)
                return match;
        }

        return null;
    }

    /// <summary>
    /// Determines if this type is a stream-related handler or behavior.
    /// </summary>
    /// <returns>True if the type is stream-related, false otherwise.</returns>
    public bool IsStreamType()
    {
        return ImplementsInterface(typeof(DispatchR.Requests.Stream.IStreamRequestHandler<,>)) ||
               ImplementsInterface(typeof(DispatchR.Requests.Stream.IStreamPipelineBehavior<,>));
    }

    /// <summary>
    /// Gets the handler interface type for this type.
    /// </summary>
    /// <returns>The handler interface type.</returns>
    /// <exception cref="InvalidOperationException">Thrown when no handler interface is found.</exception>
    public Type GetHandlerInterface()
    {
        var handlerInterface = GetFirstMatchingGenericInterface(
            typeof(DispatchR.Requests.Send.IRequestHandler<,>),
            typeof(DispatchR.Requests.Stream.IStreamRequestHandler<,>));

        return handlerInterface ?? throw new InvalidOperationException(
            $"Type {Type.Name} does not implement a recognized handler interface.");
    }

    private static Type[] GetCachedInterfaces(Type type)
    {
        return InterfaceCache.GetOrAdd(type, t => t.GetInterfaces());
    }

    public override string ToString() => Type.Name;
    
    public override bool Equals(object? obj) => obj is TypeInfo other && Type == other.Type;
    
    public override int GetHashCode() => Type.GetHashCode();
}
