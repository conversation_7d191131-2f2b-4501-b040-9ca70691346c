using System.Reflection;
using DispatchR.Internal;
using DispatchR.Requests;
using DispatchR.Requests.Notification;
using DispatchR.Requests.Send;
using DispatchR.Requests.Stream;
using Xunit;

namespace DispatchR.Tests.Internal;

public class TypeRegistryTests
{
    [Fact]
    public void CreateFromAssembly_WithNullAssembly_ThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => TypeRegistry.CreateFromAssembly(null!));
    }

    [Fact]
    public void CreateFromAssembly_WithValidAssembly_ReturnsTypeRegistry()
    {
        // Arrange
        var assembly = Assembly.GetExecutingAssembly();

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.NotNull(registry);
    }

    [Fact]
    public void CreateFromAssembly_WithRequestHandlers_PopulatesRequestHandlers()
    {
        // Arrange
        var assembly = typeof(TestRequestHandler).Assembly;

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.NotEmpty(registry.RequestHandlers);
        Assert.Contains(registry.RequestHandlers, h => h.Type == typeof(TestRequestHandler));
    }

    [Fact]
    public void CreateFromAssembly_WithStreamHandlers_PopulatesStreamHandlers()
    {
        // Arrange
        var assembly = typeof(TestStreamHandler).Assembly;

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.NotEmpty(registry.StreamHandlers);
        Assert.Contains(registry.StreamHandlers, h => h.Type == typeof(TestStreamHandler));
    }

    [Fact]
    public void CreateFromAssembly_WithPipelineBehaviors_PopulatesPipelineBehaviors()
    {
        // Arrange
        var assembly = typeof(TestPipelineBehavior).Assembly;

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.NotEmpty(registry.PipelineBehaviors);
        Assert.Contains(registry.PipelineBehaviors, p => p.Type == typeof(TestPipelineBehavior));
    }

    [Fact]
    public void CreateFromAssembly_WithNotificationHandlers_PopulatesNotificationHandlers()
    {
        // Arrange
        var assembly = typeof(TestNotificationHandler).Assembly;

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.NotEmpty(registry.NotificationHandlers);
        Assert.Contains(registry.NotificationHandlers.SelectMany(g => g), h => h.Type == typeof(TestNotificationHandler));
    }

    [Fact]
    public void HasHandlers_WithRequestHandlers_ReturnsTrue()
    {
        // Arrange
        var assembly = typeof(TestRequestHandler).Assembly;

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.True(registry.HasHandlers);
    }

    [Fact]
    public void HasHandlers_WithStreamHandlers_ReturnsTrue()
    {
        // Arrange
        var assembly = typeof(TestStreamHandler).Assembly;

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.True(registry.HasHandlers);
    }

    [Fact]
    public void HasNotifications_WithNotificationHandlers_ReturnsTrue()
    {
        // Arrange
        var assembly = typeof(TestNotificationHandler).Assembly;

        // Act
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Assert
        Assert.True(registry.HasNotifications);
    }

    [Fact]
    public void GetMatchingPipelines_WithNullHandlerInterface_ThrowsArgumentNullException()
    {
        // Arrange
        var assembly = Assembly.GetExecutingAssembly();
        var registry = TypeRegistry.CreateFromAssembly(assembly);

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => registry.GetMatchingPipelines(null!, false));
    }

    [Fact]
    public void GetMatchingPipelines_WithMatchingPipeline_ReturnsPipeline()
    {
        // Arrange
        var assembly = typeof(TestPipelineBehavior).Assembly;
        var registry = TypeRegistry.CreateFromAssembly(assembly);
        var handlerInterface = typeof(IRequestHandler<TestRequest, int>);

        // Act
        var pipelines = registry.GetMatchingPipelines(handlerInterface, false);

        // Assert
        Assert.NotEmpty(pipelines);
        Assert.Contains(pipelines, p => p.Type == typeof(TestPipelineBehavior));
    }
}


