﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <PackageReadmeFile>README.md</PackageReadmeFile>
        <PackageId>DispatchR.Mediator</PackageId>
        <Authors>hasanxdev</Authors>
        <Description>
            Fast, zero-alloc alternative to MediatR for .NET – minimal, blazing fast, and DI-friendly.
        </Description>
        <PackageTags>DispatchR;Mediator;MediatR</PackageTags>
        <PackageProjectUrl>https://github.com/hasanxdev/DispatchR</PackageProjectUrl>
        <RepositoryUrl>https://github.com/hasanxdev/DispatchR</RepositoryUrl>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
    </ItemGroup>

    <ItemGroup>
      <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
        <_Parameter1>DispatchR.Tests</_Parameter1>
      </AssemblyAttribute>
    </ItemGroup>

    <ItemGroup>
        <None Include="..\..\README.md" Pack="true" PackagePath="\" />
    </ItemGroup>

</Project>
