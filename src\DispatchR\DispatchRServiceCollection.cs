using System.Reflection;
using DispatchR.Internal;
using DispatchR.Requests;
using DispatchR.Requests.Notification;
using DispatchR.Requests.Send;
using Microsoft.Extensions.DependencyInjection;

namespace DispatchR;

/// <summary>
/// Extension methods for registering DispatchR services with dependency injection container.
/// </summary>
public static class DispatchRServiceCollection
{
    /// <summary>
    /// Registers DispatchR services including handlers, pipelines, and notifications from the specified assembly.
    /// </summary>
    /// <param name="services">The service collection to register services with.</param>
    /// <param name="assembly">The assembly to scan for handlers and behaviors.</param>
    /// <param name="withPipelines">Whether to register pipeline behaviors. Default is true.</param>
    /// <param name="withNotifications">Whether to register notification handlers. Default is true.</param>
    /// <exception cref="ArgumentNullException">Thrown when services or assembly is null.</exception>
    /// <exception cref="InvalidOperationException">Thrown when assembly scanning fails.</exception>
    public static IServiceCollection AddDispatchR(this IServiceCollection services, Assembly assembly,
        bool withPipelines = true, bool withNotifications = true)
    {
        // Input validation
        ArgumentNullException.ThrowIfNull(services, nameof(services));
        ArgumentNullException.ThrowIfNull(assembly, nameof(assembly));

        try
        {
            // Register core mediator service
            services.AddScoped<IMediator, Mediator>();

            // Create type registry for efficient type lookups
            var typeRegistry = TypeRegistry.CreateFromAssembly(assembly);

            // Register services based on configuration
            if (withNotifications)
            {
                RegisterNotificationHandlers(services, typeRegistry);
            }

            if (typeRegistry.HasHandlers)
            {
                RegisterRequestHandlers(services, typeRegistry, withPipelines);
            }

            return services;
        }
        catch (ReflectionTypeLoadException ex)
        {
            throw new InvalidOperationException(
                $"Failed to load types from assembly '{assembly.FullName}'. " +
                $"Loader exceptions: {string.Join(", ", ex.LoaderExceptions?.Select(e => e?.Message) ?? [])}",
                ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(
                $"Failed to register DispatchR services from assembly '{assembly.FullName}'.", ex);
        }
    }

    /// <summary>
    /// Registers notification handlers from the type registry.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="typeRegistry">The type registry containing notification handlers.</param>
    private static void RegisterNotificationHandlers(IServiceCollection services, TypeRegistry typeRegistry)
    {
        if (!typeRegistry.HasNotifications) return;

        foreach (var notificationGroup in typeRegistry.NotificationHandlers)
        {
            var notificationType = notificationGroup.Key;
            var handlerServiceType = typeof(INotificationHandler<>).MakeGenericType(notificationType);

            foreach (var handlerTypeInfo in notificationGroup)
            {
                services.AddScoped(handlerServiceType, handlerTypeInfo.Type);
            }
        }
    }

    /// <summary>
    /// Registers request handlers and their associated pipeline behaviors.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="typeRegistry">The type registry containing handlers and behaviors.</param>
    /// <param name="withPipelines">Whether to register pipeline behaviors.</param>
    private static void RegisterRequestHandlers(IServiceCollection services, TypeRegistry typeRegistry, bool withPipelines)
    {
        RegisterHandlerGroup(services, typeRegistry.RequestHandlers, typeRegistry, withPipelines, isStream: false);
        RegisterHandlerGroup(services, typeRegistry.StreamHandlers, typeRegistry, withPipelines, isStream: true);
    }

    /// <summary>
    /// Registers a group of handlers (either regular or stream handlers) with their pipelines.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="handlers">The handlers to register.</param>
    /// <param name="typeRegistry">The type registry for pipeline lookup.</param>
    /// <param name="withPipelines">Whether to register pipeline behaviors.</param>
    /// <param name="isStream">Whether these are stream handlers.</param>
    private static void RegisterHandlerGroup(IServiceCollection services, IReadOnlyList<Internal.TypeInfo> handlers,
        TypeRegistry typeRegistry, bool withPipelines, bool isStream)
    {
        foreach (var handler in handlers)
        {
            RegisterSingleHandler(services, handler, typeRegistry, withPipelines, isStream);
        }
    }

    /// <summary>
    /// Registers a single handler with its associated pipeline behaviors.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="handler">The handler to register.</param>
    /// <param name="typeRegistry">The type registry for pipeline lookup.</param>
    /// <param name="withPipelines">Whether to register pipeline behaviors.</param>
    /// <param name="isStream">Whether this is a stream handler.</param>
    private static void RegisterSingleHandler(IServiceCollection services, Internal.TypeInfo handler,
        TypeRegistry typeRegistry, bool withPipelines, bool isStream)
    {
        try
        {
            // Register the handler itself as a keyed service
            services.AddKeyedScoped(typeof(IRequestHandler), handler.Key, handler.Type);

            // Get the handler interface
            var handlerInterface = handler.GetHandlerInterface();

            // Register pipelines if enabled
            if (withPipelines)
            {
                var matchingPipelines = typeRegistry.GetMatchingPipelines(handlerInterface, isStream);
                RegisterPipelinesForHandler(services, handler.Key, handlerInterface, matchingPipelines);
            }

            // Register the final service factory
            RegisterHandlerServiceFactory(services, handlerInterface, handler.Key);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(
                $"Failed to register handler '{handler.Type.Name}'. {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Registers pipeline behaviors for a specific handler.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="handlerKey">The unique key for the handler.</param>
    /// <param name="handlerInterface">The handler interface type.</param>
    /// <param name="pipelines">The pipeline behaviors to register.</param>
    private static void RegisterPipelinesForHandler(IServiceCollection services, Guid handlerKey,
        Type handlerInterface, IEnumerable<Internal.TypeInfo> pipelines)
    {
        foreach (var pipeline in pipelines)
        {
            try
            {
                Type serviceType;
                if (pipeline.Type.IsGenericTypeDefinition)
                {
                    // Create closed generic type from handler interface arguments
                    var genericArgs = handlerInterface.GetGenericArguments();
                    serviceType = pipeline.Type.MakeGenericType(genericArgs);
                }
                else
                {
                    serviceType = pipeline.Type;
                }

                services.AddKeyedScoped(typeof(IRequestHandler), handlerKey, serviceType);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Failed to register pipeline '{pipeline.Type.Name}' for handler interface '{handlerInterface.Name}'. {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// Registers the service factory that creates the complete pipeline chain for a handler.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="handlerInterface">The handler interface type.</param>
    /// <param name="handlerKey">The unique key for the handler.</param>
    private static void RegisterHandlerServiceFactory(IServiceCollection services, Type handlerInterface, Guid handlerKey)
    {
        services.AddScoped(handlerInterface, serviceProvider =>
        {
            var handlersAndPipelines = serviceProvider.GetKeyedServices<IRequestHandler>(handlerKey).ToArray();

            if (handlersAndPipelines.Length == 0)
            {
                throw new InvalidOperationException($"No handlers registered for key '{handlerKey}'.");
            }

            // If only one handler (no pipelines), return it directly
            if (handlersAndPipelines.Length == 1)
            {
                return handlersAndPipelines[0];
            }

            // Build the pipeline chain using Chain of Responsibility pattern
            return BuildPipelineChain(handlersAndPipelines);
        });
    }

    /// <summary>
    /// Builds a pipeline chain from an array of handlers and behaviors using the Chain of Responsibility pattern.
    /// </summary>
    /// <param name="handlersAndPipelines">Array of handlers and pipeline behaviors.</param>
    /// <returns>The root of the pipeline chain.</returns>
    private static IRequestHandler BuildPipelineChain(IRequestHandler[] handlersAndPipelines)
    {
        if (handlersAndPipelines.Length == 0)
        {
            throw new ArgumentException("Cannot build pipeline chain from empty array.", nameof(handlersAndPipelines));
        }

        // The first item is the actual handler, the rest are pipeline behaviors
        var rootHandler = handlersAndPipelines[0];

        // Chain the pipeline behaviors in reverse order (last behavior wraps the handler)
        for (int i = 1; i < handlersAndPipelines.Length; i++)
        {
            var currentPipeline = handlersAndPipelines[i];
            currentPipeline.SetNext(rootHandler);
            rootHandler = currentPipeline;
        }

        return rootHandler;
    }

}