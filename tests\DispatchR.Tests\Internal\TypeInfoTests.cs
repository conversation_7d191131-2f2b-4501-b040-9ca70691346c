using DispatchR.Internal;
using DispatchR.Requests;
using DispatchR.Requests.Notification;
using DispatchR.Requests.Send;
using DispatchR.Requests.Stream;
using Xunit;

namespace DispatchR.Tests.Internal;

public class TypeInfoTests
{
    [Fact]
    public void Constructor_WithNullType_ThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new TypeInfo(null!));
    }

    [Fact]
    public void Constructor_WithValidType_SetsProperties()
    {
        // Arrange
        var type = typeof(TestRequestHandler);

        // Act
        var typeInfo = new TypeInfo(type);

        // Assert
        Assert.Equal(type, typeInfo.Type);
        Assert.Equal(type.GUID, typeInfo.Key);
        Assert.NotEmpty(typeInfo.Interfaces);
    }

    [Fact]
    public void ImplementsInterface_WithNullInterface_ThrowsArgumentNullException()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => typeInfo.ImplementsInterface(null!));
    }

    [Fact]
    public void ImplementsInterface_WithNonGenericInterface_ThrowsArgumentException()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act & Assert
        Assert.Throws<ArgumentException>(() => typeInfo.ImplementsInterface(typeof(IDisposable)));
    }

    [Fact]
    public void ImplementsInterface_WithImplementedInterface_ReturnsTrue()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.ImplementsInterface(typeof(IRequestHandler<,>));

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void ImplementsInterface_WithNotImplementedInterface_ReturnsFalse()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.ImplementsInterface(typeof(IStreamRequestHandler<,>));

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetGenericInterface_WithNullInterface_ThrowsArgumentNullException()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => typeInfo.GetGenericInterface(null!));
    }

    [Fact]
    public void GetGenericInterface_WithImplementedInterface_ReturnsInterface()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.GetGenericInterface(typeof(IRequestHandler<,>));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(typeof(IRequestHandler<TestRequest, int>), result);
    }

    [Fact]
    public void GetGenericInterface_WithNotImplementedInterface_ReturnsNull()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.GetGenericInterface(typeof(IStreamRequestHandler<,>));

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetFirstMatchingGenericInterface_WithNullInterfaces_ThrowsArgumentNullException()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => typeInfo.GetFirstMatchingGenericInterface(null!));
    }

    [Fact]
    public void GetFirstMatchingGenericInterface_WithMatchingInterface_ReturnsFirstMatch()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.GetFirstMatchingGenericInterface(
            typeof(IStreamRequestHandler<,>), 
            typeof(IRequestHandler<,>));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(typeof(IRequestHandler<TestRequest, int>), result);
    }

    [Fact]
    public void IsStreamType_WithStreamHandler_ReturnsTrue()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestStreamHandler));

        // Act
        var result = typeInfo.IsStreamType();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsStreamType_WithRegularHandler_ReturnsFalse()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.IsStreamType();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetHandlerInterface_WithRequestHandler_ReturnsHandlerInterface()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.GetHandlerInterface();

        // Assert
        Assert.Equal(typeof(IRequestHandler<TestRequest, int>), result);
    }

    [Fact]
    public void GetHandlerInterface_WithStreamHandler_ReturnsStreamHandlerInterface()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestStreamHandler));

        // Act
        var result = typeInfo.GetHandlerInterface();

        // Assert
        Assert.Equal(typeof(IStreamRequestHandler<TestStreamRequest, string>), result);
    }

    [Fact]
    public void GetHandlerInterface_WithNonHandler_ThrowsInvalidOperationException()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(string));

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => typeInfo.GetHandlerInterface());
    }

    [Fact]
    public void ToString_ReturnsTypeName()
    {
        // Arrange
        var typeInfo = new TypeInfo(typeof(TestRequestHandler));

        // Act
        var result = typeInfo.ToString();

        // Assert
        Assert.Equal("TestRequestHandler", result);
    }

    [Fact]
    public void Equals_WithSameType_ReturnsTrue()
    {
        // Arrange
        var typeInfo1 = new TypeInfo(typeof(TestRequestHandler));
        var typeInfo2 = new TypeInfo(typeof(TestRequestHandler));

        // Act & Assert
        Assert.True(typeInfo1.Equals(typeInfo2));
        Assert.Equal(typeInfo1.GetHashCode(), typeInfo2.GetHashCode());
    }

    [Fact]
    public void Equals_WithDifferentType_ReturnsFalse()
    {
        // Arrange
        var typeInfo1 = new TypeInfo(typeof(TestRequestHandler));
        var typeInfo2 = new TypeInfo(typeof(TestStreamHandler));

        // Act & Assert
        Assert.False(typeInfo1.Equals(typeInfo2));
    }
}


