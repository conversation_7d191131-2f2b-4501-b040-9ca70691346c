using System.Reflection;
using DispatchR.Requests;
using DispatchR.Requests.Notification;
using DispatchR.Requests.Send;
using DispatchR.Requests.Stream;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace DispatchR.Tests;

public class DispatchRServiceCollectionTests
{
    [Fact]
    public void AddDispatchR_WithNullServices_ThrowsArgumentNullException()
    {
        // Arrange
        IServiceCollection? services = null;
        var assembly = Assembly.GetExecutingAssembly();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => services!.AddDispatchR(assembly));
    }

    [Fact]
    public void AddDispatchR_WithNullAssembly_ThrowsArgumentNullException()
    {
        // Arrange
        var services = new ServiceCollection();
        Assembly? assembly = null;

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => services.AddDispatchR(assembly!));
    }

    [Fact]
    public void AddDispatchR_WithValidAssembly_RegistersMediator()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = Assembly.GetExecutingAssembly();

        // Act
        services.AddDispatchR(assembly);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var mediator = serviceProvider.GetService<IMediator>();
        Assert.NotNull(mediator);
        Assert.IsType<Mediator>(mediator);
    }

    [Fact]
    public void AddDispatchR_WithRequestHandler_RegistersHandler()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = typeof(TestRequestHandler).Assembly;

        // Act
        services.AddDispatchR(assembly);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var handler = serviceProvider.GetService<IRequestHandler<TestRequest, int>>();
        Assert.NotNull(handler);
    }

    [Fact]
    public void AddDispatchR_WithNotificationHandler_RegistersHandler()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = typeof(TestNotificationHandler).Assembly;

        // Act
        services.AddDispatchR(assembly, withNotifications: true);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var handlers = serviceProvider.GetServices<INotificationHandler<TestNotification>>();
        Assert.NotEmpty(handlers);
    }

    [Fact]
    public void AddDispatchR_WithNotificationsDisabled_DoesNotRegisterNotificationHandlers()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = typeof(TestNotificationHandler).Assembly;

        // Act
        services.AddDispatchR(assembly, withNotifications: false);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var handlers = serviceProvider.GetServices<INotificationHandler<TestNotification>>();
        Assert.Empty(handlers);
    }

    [Fact]
    public void AddDispatchR_WithPipelineBehavior_RegistersPipeline()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = typeof(TestPipelineBehavior).Assembly;

        // Act
        services.AddDispatchR(assembly, withPipelines: true);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var handler = serviceProvider.GetService<IRequestHandler<TestRequest, int>>();
        Assert.NotNull(handler);
        
        // Verify pipeline is in the chain
        Assert.IsType<TestPipelineBehavior>(handler);
    }

    [Fact]
    public void AddDispatchR_WithPipelinesDisabled_DoesNotRegisterPipelines()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = typeof(TestPipelineBehavior).Assembly;

        // Act
        services.AddDispatchR(assembly, withPipelines: false);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var handler = serviceProvider.GetService<IRequestHandler<TestRequest, int>>();
        Assert.NotNull(handler);
        
        // Verify no pipeline in the chain
        Assert.IsType<TestRequestHandler>(handler);
    }

    [Fact]
    public void AddDispatchR_WithStreamHandler_RegistersStreamHandler()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = typeof(TestStreamHandler).Assembly;

        // Act
        services.AddDispatchR(assembly);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var handler = serviceProvider.GetService<IStreamRequestHandler<TestStreamRequest, string>>();
        Assert.NotNull(handler);
    }

    [Fact]
    public void AddDispatchR_ReturnsServiceCollection_ForChaining()
    {
        // Arrange
        var services = new ServiceCollection();
        var assembly = Assembly.GetExecutingAssembly();

        // Act
        var result = services.AddDispatchR(assembly);

        // Assert
        Assert.Same(services, result);
    }
}


