﻿
using System.Runtime.CompilerServices;
using DispatchR.Requests.Stream;

namespace Sample.DispatchR.StreamRequest;

public class CounterPipelineStreamHandler : IStreamPipelineBehavior<CounterStreamRequest, string>
{
    public required IStreamRequestHandler<CounterStreamRequest, string> NextPipeline { get; set; }
    
    public async IAsyncEnumerable<string> <PERSON>le(CounterStreamRequest request, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        await foreach (var response in NextPipeline.Handle(request, cancellationToken).ConfigureAwait(false))
        {
            yield return response;
        }
    }
}