name: Release

on:
  push:
    tags:
      - '*.*.*'
      
jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest]
      fail-fast: false
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Setup dotnet
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.x'
          
      - name: Restore
        run: dotnet restore src/DispatchR/DispatchR.csproj
        
      - name: Build
        run: dotnet build src/DispatchR/DispatchR.csproj --configuration Release --no-restore
        
      - name: Extract version from tag
        id: get_version
        run: echo "version=${GITHUB_REF#refs/tags/v}" >> "$GITHUB_OUTPUT"

      - name: Pack project
        run: dotnet pack src/DispatchR/DispatchR.csproj --configuration Release --no-build -o ./nupkgs /p:PackageVersion=${{ steps.get_version.outputs.version }}

      - name: Push to NuGet
        run: dotnet nuget push "./nupkgs/*.nupkg" --api-key ${{ secrets.NUGET_API_KEY }} --source https://api.nuget.org/v3/index.json