using System.Runtime.CompilerServices;
using DispatchR.Requests;
using DispatchR.Requests.Notification;
using DispatchR.Requests.Send;
using DispatchR.Requests.Stream;

namespace DispatchR.Tests;

// Test types for the unit tests
public record TestRequest : IRequest<TestRequest, int>;

public class TestRequestHandler : IRequestHandler<TestRequest, int>
{
    public int Handle(TestRequest request, CancellationToken cancellationToken) => 42;
}

public class TestPipelineBehavior : IPipelineBehavior<TestRequest, int>
{
    public required IRequestHandler<TestRequest, int> NextPipeline { get; set; }
    
    public int Handle(TestRequest request, CancellationToken cancellationToken)
    {
        return NextPipeline.Handle(request, cancellationToken);
    }
}

public record TestNotification : INotification;

public class TestNotificationHandler : INotificationHandler<TestNotification>
{
    public ValueTask Handle(TestNotification request, CancellationToken cancellationToken) => ValueTask.CompletedTask;
}

public record TestStreamRequest : IStreamRequest<TestStreamRequest, string>;

public class TestStreamHandler : IStreamRequestHandler<TestStreamRequest, string>
{
    public async IAsyncEnumerable<string> Handle(TestStreamRequest request, [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        yield return "test";
    }
}
