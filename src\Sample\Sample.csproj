<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0"/>
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.4.7" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DispatchR\DispatchR.csproj" />
    </ItemGroup>

</Project>
