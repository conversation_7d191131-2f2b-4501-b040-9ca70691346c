using System.Collections.Concurrent;
using System.Reflection;
using DispatchR.Requests.Notification;
using DispatchR.Requests.Send;
using DispatchR.Requests.Stream;

namespace DispatchR.Internal;

/// <summary>
/// Internal registry for efficiently managing and categorizing types from assemblies.
/// Provides optimized lookups for handlers, pipelines, and notifications.
/// </summary>
internal sealed class TypeRegistry
{
    private static readonly Type[] SupportedInterfaceTypes = 
    {
        typeof(IRequestHandler<,>),
        typeof(IPipelineBehavior<,>),
        typeof(IStreamRequestHandler<,>),
        typeof(IStreamPipelineBehavior<,>),
        typeof(INotificationHandler<>)
    };

    private readonly ConcurrentDictionary<Type, TypeInfo> _typeCache = new();
    
    public IReadOnlyList<TypeInfo> RequestHandlers { get; private set; } = Array.Empty<TypeInfo>();
    public IReadOnlyList<TypeInfo> StreamHandlers { get; private set; } = Array.Empty<TypeInfo>();
    public IReadOnlyList<TypeInfo> PipelineBehaviors { get; private set; } = Array.Empty<TypeInfo>();
    public IReadOnlyList<TypeInfo> StreamPipelineBehaviors { get; private set; } = Array.Empty<TypeInfo>();
    public IReadOnlyList<IGrouping<Type, TypeInfo>> NotificationHandlers { get; private set; } = Array.Empty<IGrouping<Type, TypeInfo>>();
    
    public bool HasHandlers => RequestHandlers.Count > 0 || StreamHandlers.Count > 0;
    public bool HasNotifications => NotificationHandlers.Any();

    private TypeRegistry() { }

    /// <summary>
    /// Creates a type registry by scanning the specified assembly for supported types.
    /// </summary>
    /// <param name="assembly">The assembly to scan.</param>
    /// <returns>A populated type registry.</returns>
    /// <exception cref="ArgumentNullException">Thrown when assembly is null.</exception>
    public static TypeRegistry CreateFromAssembly(Assembly assembly)
    {
        ArgumentNullException.ThrowIfNull(assembly, nameof(assembly));

        var allTypes = GetValidTypesFromAssembly(assembly);
        var categorizedTypes = CategorizeTypes(allTypes);

        return new TypeRegistry
        {
            RequestHandlers = categorizedTypes.RequestHandlers,
            StreamHandlers = categorizedTypes.StreamHandlers,
            PipelineBehaviors = categorizedTypes.PipelineBehaviors,
            StreamPipelineBehaviors = categorizedTypes.StreamPipelineBehaviors,
            NotificationHandlers = categorizedTypes.NotificationHandlers
        };
    }

    /// <summary>
    /// Gets pipeline behaviors that match the specified handler interface.
    /// </summary>
    /// <param name="handlerInterface">The handler interface to match against.</param>
    /// <param name="isStream">Whether to look for stream pipeline behaviors.</param>
    /// <returns>Collection of matching pipeline behaviors.</returns>
    public IEnumerable<TypeInfo> GetMatchingPipelines(Type handlerInterface, bool isStream)
    {
        ArgumentNullException.ThrowIfNull(handlerInterface, nameof(handlerInterface));

        var pipelines = isStream ? StreamPipelineBehaviors : PipelineBehaviors;
        var targetBehaviorType = isStream ? typeof(IStreamPipelineBehavior<,>) : typeof(IPipelineBehavior<,>);

        return pipelines.Where(pipeline => 
            DoesInterfaceMatch(pipeline.Type, handlerInterface, targetBehaviorType));
    }

    private static IReadOnlyList<Type> GetValidTypesFromAssembly(Assembly assembly)
    {
        try
        {
            return assembly.GetTypes()
                .Where(IsValidType)
                .Where(HasSupportedInterfaces)
                .ToList();
        }
        catch (ReflectionTypeLoadException ex)
        {
            // Return only successfully loaded types, log the failures
            var loadedTypes = ex.Types.Where(t => t != null).Cast<Type>().ToList();
            return loadedTypes.Where(IsValidType).Where(HasSupportedInterfaces).ToList();
        }
    }

    private static bool IsValidType(Type type)
    {
        return type is { IsClass: true, IsAbstract: false, IsGenericTypeDefinition: false };
    }

    private static bool HasSupportedInterfaces(Type type)
    {
        var interfaces = type.GetInterfaces();
        if (interfaces.Length == 0) return false;

        return interfaces
            .Where(i => i.IsGenericType)
            .Select(i => i.GetGenericTypeDefinition())
            .Any(SupportedInterfaceTypes.Contains);
    }

    private static CategorizedTypes CategorizeTypes(IReadOnlyList<Type> types)
    {
        var requestHandlers = new List<TypeInfo>();
        var streamHandlers = new List<TypeInfo>();
        var pipelineBehaviors = new List<TypeInfo>();
        var streamPipelineBehaviors = new List<TypeInfo>();
        var notificationHandlers = new List<TypeInfo>();

        foreach (var type in types)
        {
            var typeInfo = new TypeInfo(type);

            // Categorize based on most specific interface first
            // Pipeline behaviors inherit from handlers, so check them first
            if (typeInfo.ImplementsInterface(typeof(IPipelineBehavior<,>)))
            {
                pipelineBehaviors.Add(typeInfo);
            }
            else if (typeInfo.ImplementsInterface(typeof(IStreamPipelineBehavior<,>)))
            {
                streamPipelineBehaviors.Add(typeInfo);
            }
            else if (typeInfo.ImplementsInterface(typeof(IRequestHandler<,>)))
            {
                requestHandlers.Add(typeInfo);
            }
            else if (typeInfo.ImplementsInterface(typeof(IStreamRequestHandler<,>)))
            {
                streamHandlers.Add(typeInfo);
            }

            // Notification handlers are separate and can coexist with other interfaces
            if (typeInfo.ImplementsInterface(typeof(INotificationHandler<>)))
                notificationHandlers.Add(typeInfo);
        }

        var groupedNotifications = notificationHandlers
            .GroupBy(GetNotificationTypeFromHandler)
            .ToList();

        return new CategorizedTypes(
            requestHandlers,
            streamHandlers,
            pipelineBehaviors,
            streamPipelineBehaviors,
            groupedNotifications);
    }

    private static Type GetNotificationTypeFromHandler(TypeInfo typeInfo)
    {
        var notificationInterface = typeInfo.GetGenericInterface(typeof(INotificationHandler<>));
        return notificationInterface?.GetGenericArguments()[0] 
               ?? throw new InvalidOperationException($"Could not determine notification type for {typeInfo.Type.Name}");
    }

    private static bool DoesInterfaceMatch(Type pipelineType, Type handlerInterface, Type targetBehaviorType)
    {
        var pipelineInterface = pipelineType.GetInterfaces()
            .FirstOrDefault(i => i.IsGenericType && i.GetGenericTypeDefinition() == targetBehaviorType);

        return pipelineInterface?.GetGenericArguments().SequenceEqual(handlerInterface.GetGenericArguments()) == true;
    }

    private record CategorizedTypes(
        IReadOnlyList<TypeInfo> RequestHandlers,
        IReadOnlyList<TypeInfo> StreamHandlers,
        IReadOnlyList<TypeInfo> PipelineBehaviors,
        IReadOnlyList<TypeInfo> StreamPipelineBehaviors,
        IReadOnlyList<IGrouping<Type, TypeInfo>> NotificationHandlers);
}
